import { NextRequest, NextResponse } from 'next/server';
import { Ratelimit } from '@upstash/ratelimit';
import { Redis } from '@upstash/redis';

// Initialize Redis for rate limiting (same as middleware)
let redis: Redis | null = null;
let isRedisConfigured = false;

try {
  if (process.env.UPSTASH_REDIS_REST_URL &&
      process.env.UPSTASH_REDIS_REST_TOKEN &&
      process.env.UPSTASH_REDIS_REST_URL !== 'https://example.upstash.io') {
    redis = new Redis({
      url: process.env.UPSTASH_REDIS_REST_URL,
      token: process.env.UPSTASH_REDIS_REST_TOKEN,
    });
    isRedisConfigured = true;
  }
} catch (error) {
  console.warn('Redis not configured, using in-memory fallback for development');
}

// Rate limit configurations (same as middleware)
let rateLimits: { [key: string]: Ratelimit } = {};

if (isRedisConfigured && redis) {
  rateLimits = {
    chat: new Ratelimit({
      redis,
      limiter: Ratelimit.slidingWindow(10, '5 m'), // 10 requests per 5 minutes
      analytics: true,
    }),
  };
}

// In-memory fallback for development (same as middleware)
const inMemoryLimits = new Map<string, { count: number; resetTime: number }>();

function checkInMemoryRateLimit(key: string, limit: number, windowMs: number): { success: boolean; limit: number; reset: number; remaining: number } {
  const now = Date.now();
  const stored = inMemoryLimits.get(key);

  if (!stored || now > stored.resetTime) {
    inMemoryLimits.set(key, { count: 0, resetTime: now + windowMs });
    return { success: true, limit, reset: now + windowMs, remaining: limit };
  }

  return { success: stored.count < limit, limit, reset: stored.resetTime, remaining: limit - stored.count };
}

// Get client IP address (same as middleware)
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');

  if (cfConnectingIP) return cfConnectingIP;
  if (realIP) return realIP;
  if (forwarded) return forwarded.split(',')[0].trim();

  return 'unknown';
}

export async function GET(request: NextRequest) {
  try {
    const ip = getClientIP(request);
    const rateLimitType = 'chat'; // We're checking chat rate limits

    let success: boolean, limit: number, reset: number, remaining: number;

    if (isRedisConfigured && rateLimits[rateLimitType]) {
      // Use Redis-based rate limiting - check without incrementing
      const result = await rateLimits[rateLimitType].limit(ip);
      ({ success, limit, reset, remaining } = result);
      
      // Since we just checked, we need to decrement the count back
      // This is a limitation of the current approach - we'll need to implement a separate check method
      // For now, we'll use the current state
    } else {
      // Use in-memory fallback for development
      const config = { limit: 10, windowMs: 5 * 60 * 1000 }; // 10 requests per 5 minutes
      const key = `${rateLimitType}:${ip}`;
      const result = checkInMemoryRateLimit(key, config.limit, config.windowMs);
      ({ success, limit, reset, remaining } = result);
    }

    return NextResponse.json({
      success,
      limit,
      remaining,
      resetTime: reset,
      type: rateLimitType
    });

  } catch (error) {
    console.error('Rate limit status check error:', error);
    // Return default values if check fails
    return NextResponse.json({
      success: true,
      limit: 10,
      remaining: 10,
      resetTime: Date.now() + (5 * 60 * 1000),
      type: 'chat'
    });
  }
}
