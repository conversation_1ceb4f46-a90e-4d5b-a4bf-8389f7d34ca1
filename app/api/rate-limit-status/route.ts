import { NextRequest, NextResponse } from 'next/server';
import { Ratelimit } from '@upstash/ratelimit';
import { Redis } from '@upstash/redis';

// Initialize Redis for rate limiting (same as middleware)
let redis: Redis | null = null;
let isRedisConfigured = false;

try {
  if (process.env.UPSTASH_REDIS_REST_URL &&
      process.env.UPSTASH_REDIS_REST_TOKEN &&
      process.env.UPSTASH_REDIS_REST_URL !== 'https://example.upstash.io') {
    redis = new Redis({
      url: process.env.UPSTASH_REDIS_REST_URL,
      token: process.env.UPSTASH_REDIS_REST_TOKEN,
    });
    isRedisConfigured = true;
  }
} catch (error) {
  console.warn('Redis not configured, using in-memory fallback for development');
}

// Rate limit configurations (same as middleware)
let rateLimits: { [key: string]: Ratelimit } = {};

if (isRedisConfigured && redis) {
  rateLimits = {
    chat: new Ratelimit({
      redis,
      limiter: Ratelimit.slidingWindow(10, '5 m'), // 10 requests per 5 minutes
      analytics: true,
    }),
  };
}

// Helper function to check rate limit status WITHOUT consuming a token
async function checkRateLimitStatusOnly(rateLimitType: string, ip: string): Promise<{ success: boolean; limit: number; reset: number; remaining: number }> {
  if (!isRedisConfigured || !redis || !rateLimits[rateLimitType]) {
    // Fallback to in-memory for development
    const config = { limit: 10, windowMs: 5 * 60 * 1000 }; // 10 requests per 5 minutes
    const key = `${rateLimitType}:${ip}`;
    return checkInMemoryRateLimit(key, config.limit, config.windowMs);
  }

  // For Redis-based rate limiting, we need to manually check the sliding window
  // without consuming a token. We'll use the same logic as Upstash Ratelimit
  // but without incrementing the counter.

  const windowSize = 5 * 60 * 1000; // 5 minutes in milliseconds
  const limit = 10; // 10 requests per window
  const now = Date.now();
  const window = Math.floor(now / windowSize);

  // Create the same key format that Upstash Ratelimit uses
  const key = `ratelimit:${rateLimitType}:${ip}`;

  try {
    // Get the current window data without incrementing
    const pipeline = redis.pipeline();

    // Check current window
    pipeline.get(`${key}:${window}`);
    // Check previous window (for sliding window calculation)
    pipeline.get(`${key}:${window - 1}`);

    const results = await pipeline.exec();

    const currentCount = parseInt(results[0] as string || '0');
    const previousCount = parseInt(results[1] as string || '0');

    // Calculate sliding window count
    const windowProgress = (now % windowSize) / windowSize;
    const slidingCount = Math.floor(currentCount + previousCount * (1 - windowProgress));

    const remaining = Math.max(0, limit - slidingCount);
    const success = slidingCount < limit;
    const reset = (window + 1) * windowSize; // Next window start time

    return {
      success,
      limit,
      reset,
      remaining
    };
  } catch (error) {
    console.error('Error checking Redis rate limit status:', error);
    // Fallback to allowing the request if Redis check fails
    return {
      success: true,
      limit,
      reset: now + windowSize,
      remaining: limit
    };
  }
}

// In-memory fallback for development (same as middleware)
const inMemoryLimits = new Map<string, { count: number; resetTime: number }>();

function checkInMemoryRateLimit(key: string, limit: number, windowMs: number): { success: boolean; limit: number; reset: number; remaining: number } {
  const now = Date.now();
  const stored = inMemoryLimits.get(key);

  if (!stored || now > stored.resetTime) {
    inMemoryLimits.set(key, { count: 0, resetTime: now + windowMs });
    return { success: true, limit, reset: now + windowMs, remaining: limit };
  }

  return { success: stored.count < limit, limit, reset: stored.resetTime, remaining: limit - stored.count };
}

// Get client IP address (same as middleware)
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');

  if (cfConnectingIP) return cfConnectingIP;
  if (realIP) return realIP;
  if (forwarded) return forwarded.split(',')[0].trim();

  return 'unknown';
}

export async function GET(request: NextRequest) {
  try {
    const ip = getClientIP(request);
    const rateLimitType = 'chat'; // We're checking chat rate limits

    // Use the new non-consuming rate limit check
    const result = await checkRateLimitStatusOnly(rateLimitType, ip);
    const { success, limit, reset, remaining } = result;

    return NextResponse.json({
      success,
      limit,
      remaining,
      resetTime: reset,
      type: rateLimitType
    });

  } catch (error) {
    console.error('Rate limit status check error:', error);
    // Return default values if check fails
    return NextResponse.json({
      success: true,
      limit: 10,
      remaining: 10,
      resetTime: Date.now() + (5 * 60 * 1000),
      type: 'chat'
    });
  }
}
