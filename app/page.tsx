'use client';

import { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import type { Components } from 'react-markdown';
import 'highlight.js/styles/github.css';

interface CodeProps extends React.HTMLAttributes<HTMLElement> {
  node?: any;
  inline?: boolean;
  className?: string;
  children?: React.ReactNode;
}

const components: Components = {
  a: (props) => (
    <a className="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer" {...props}>
      {props.children}
    </a>
  ),
  strong: (props) => (
    <strong className="font-semibold" {...props}>
      {props.children}
    </strong>
  ),
  code: ({ node, inline, className, children, ...props }: CodeProps) => {
    if (inline) {
      return (
        <code className="bg-slate-100 px-1 py-0.5 rounded text-sm" {...props}>
          {children}
        </code>
      );
    }
    return (
      <pre className="bg-slate-100 p-3 rounded-md overflow-x-auto my-2">
        <code className={className} {...props}>
          {children}
        </code>
      </pre>
    );
  },
  p: (props) => (
    <p className="my-1" {...props}>
      {props.children}
    </p>
  ),
  blockquote: (props) => (
    <blockquote className="border-l-4 border-slate-300 pl-4 italic my-1 text-slate-600" {...props}>
      {props.children}
    </blockquote>
  ),
};
import {
  Send,
  Bot,
  User,
  CheckCircle,
  XCircle,
  Clock,
  Brain,
  ExternalLink,
  MessageSquare,
  Settings,
  Shield,
  LogOut,
  LogIn,
  Copy,
  Check
} from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { AuthService } from '@/lib/auth';

interface ChatMessage {
  id: string;
  text: string;
  sender: 'user' | 'bot';
  timestamp: Date;
  source?: string;
  sourceUrls?: { [title: string]: string };
  fromKnowledgeBase: boolean;
  embeddingType?: string;
}

interface RateLimitStatus {
  remaining: number;
  limit: number;
  resetTime: number;
  success: boolean;
}

export default function HomePage() {
  const [user, setUser] = useState<any>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [rateLimitStatus, setRateLimitStatus] = useState<RateLimitStatus | null>(null);
  const [knowledgeStats, setKnowledgeStats] = useState<any>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  useEffect(() => {
    // Load knowledge stats and rate limit status
    loadKnowledgeStats();
    fetchRateLimitStatus();

    // Check authentication status
    const checkAuth = async () => {
      const currentUser = await AuthService.getCurrentUser();
      setUser(currentUser);
    };
    checkAuth();

    // Listen for auth changes
    const { data: { subscription } } = AuthService.onAuthStateChange((user) => {
      setUser(user);
    });

    // Initialize with welcome message
    setMessages([{
      id: '1',
      text: 'Halo! Aku bisa bantu kamu jawab pertanyaan soal LPDP berdasarkan informasi yang aku punya',
      sender: 'bot',
      timestamp: new Date(),
      fromKnowledgeBase: true
    }]);

    return () => subscription.unsubscribe();
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const fetchRateLimitStatus = async () => {
    try {
      const response = await fetch('/api/rate-limit-status');
      if (response.ok) {
        const status = await response.json();
        setRateLimitStatus(status);
      }
    } catch (error) {
      console.error('Error fetching rate limit status:', error);
    }
  };

  const loadKnowledgeStats = async () => {
    try {
      // We'll make a simple request to get stats without authentication
      const response = await fetch('/api/chat-public', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message: '__GET_STATS__' }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.availableTopics || data.availableSources) {
          setKnowledgeStats({
            totalDocuments: data.availableSources?.length || 0,
            topics: data.availableTopics || [],
            sources: data.availableSources || []
          });
        }
      }
    } catch (error) {
      console.error('Error loading knowledge stats:', error);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const checkRateLimit = (): boolean => {
    if (!rateLimitStatus) {
      // If we don't have rate limit status yet, allow the request
      // The server will handle the actual rate limiting
      return true;
    }

    if (rateLimitStatus.remaining <= 0) {
      const resetTime = new Date(rateLimitStatus.resetTime);
      const now = new Date();
      const minutesRemaining = Math.ceil((resetTime.getTime() - now.getTime()) / 60000);

      toast.error(`Rate limit exceeded. Please wait ${minutesRemaining} minute${minutesRemaining !== 1 ? 's' : ''} before sending another message.`);
      return false;
    }

    return true;
  };

  const processMessage = async (text: string): Promise<{
    response: string;
    fromKnowledgeBase: boolean;
    source?: string;
    sourceUrls?: { [title: string]: string };
    embeddingType?: string
  }> => {
    try {
      const response = await fetch('/api/chat-public', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message: text }),
      });

      if (response.status === 429) {
        // Handle rate limit exceeded from server
        const errorData = await response.json();
        const retryAfter = errorData.retryAfter || 300; // Default to 5 minutes
        const minutesRemaining = Math.ceil(retryAfter / 60);

        toast.error(`Rate limit exceeded. Please wait ${minutesRemaining} minute${minutesRemaining !== 1 ? 's' : ''} before sending another message.`);

        // Refresh rate limit status
        await fetchRateLimitStatus();

        throw new Error('Rate limit exceeded');
      }

      if (!response.ok) {
        throw new Error('Failed to process message');
      }

      const data = await response.json();

      // Refresh rate limit status after successful request
      await fetchRateLimitStatus();

      return data;
    } catch (error) {
      console.error('Error processing message:', error);

      if (error instanceof Error && error.message === 'Rate limit exceeded') {
        return {
          response: 'Rate limit exceeded. Please wait before sending another message.',
          fromKnowledgeBase: false
        };
      }

      return {
        response: 'I apologize, but I encountered an error processing your request. Please try again later.',
        fromKnowledgeBase: false
      };
    }
  };

  const handleSendMessage = async () => {
    if (!inputText.trim()) return;
    if (!checkRateLimit()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: inputText,
      sender: 'user',
      timestamp: new Date(),
      fromKnowledgeBase: false
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsProcessing(true);

    try {
      const { response, fromKnowledgeBase, source, sourceUrls, embeddingType } = await processMessage(inputText);

      const botMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        text: response,
        sender: 'bot',
        timestamp: new Date(),
        source,
        sourceUrls,
        fromKnowledgeBase,
        embeddingType
      };

      setMessages(prev => [...prev, botMessage]);
    } catch (error) {
      console.error('Error:', error);
      toast.error('Failed to send message. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getRemainingRequests = () => {
    if (!rateLimitStatus) return 10; // Default value while loading

    const now = Date.now();
    if (now > rateLimitStatus.resetTime) return rateLimitStatus.limit;
    return Math.max(0, rateLimitStatus.remaining);
  };

  const getResetTimeMinutes = () => {
    if (!rateLimitStatus) return 5; // Default value while loading

    const now = Date.now();
    const remaining = Math.max(0, rateLimitStatus.resetTime - now);
    return Math.ceil(remaining / 60000);
  };

  const [copiedMessageId, setCopiedMessageId] = useState<string | null>(null);
  const copyTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

  const handleSourceClick = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const handleCopyToClipboard = async (text: string, messageId: string) => {
    try {
      // Check if the Clipboard API is available
      if (!navigator.clipboard) {
        throw new Error('Clipboard API not available');
      }

      await navigator.clipboard.writeText(text);
      
      setCopiedMessageId(messageId);
      
      // Clear any existing timeout
      if (copyTimeoutRef.current) {
        clearTimeout(copyTimeoutRef.current);
      }
      
      // Reset the copied state after 2 seconds
      copyTimeoutRef.current = setTimeout(() => {
        setCopiedMessageId(null);
      }, 2000);
      
      toast.success('Copied to clipboard!');
    } catch (err) {
      console.error('Failed to copy text: ', err);
      toast.error('Failed to copy text to clipboard');
    }
  };

  // Clean up timeout on component unmount
  useEffect(() => {
    return () => {
      if (copyTimeoutRef.current) {
        clearTimeout(copyTimeoutRef.current);
      }
    };
  }, []);
  
  // Clipboard API is now properly typed via standard Web API types

  const handleSignOut = async () => {
    try {
      await AuthService.signOut();
      setUser(null);
      toast.success('Successfully signed out!');
    } catch (error) {
      console.error('Sign out error:', error);
      toast.error('Failed to sign out');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-4 py-6 max-w-4xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-500 rounded-lg">
              <Bot className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-slate-800">Tanya LPDP</h1>
              <p className="text-slate-600 text-sm flex items-center gap-2">
                <Brain className="w-3 h-3" />
                AI Chat dengan sumber terkurasi
                {user && <span>• Welcome back, {user.email}</span>}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {user ? (
              <>
                <Badge variant="outline" className="flex items-center gap-1">
                  <Shield className="w-3 h-3" />
                  Authenticated
                </Badge>
                <Link href="/admin">
                  <Button variant="outline" size="sm">
                    <Settings className="w-4 h-4 mr-2" />
                    Admin Panel
                  </Button>
                </Link>
                <Button variant="outline" size="sm" onClick={handleSignOut}>
                  <LogOut className="w-4 h-4 mr-2" />
                  Sign Out
                </Button>
              </>
            ) : (
              null
            )}
          </div>
        </div>

        {/* Knowledge Base Status card removed */}

        {/* Rate Limit Status */}
        <Card className="mb-4 border-slate-200">
          <CardContent className="pt-4">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-slate-500" />
                <span className="text-slate-600">
                  Sisa request: <span className="font-medium">{getRemainingRequests()}</span>
                </span>
              </div>
              {getRemainingRequests() === 0 && (
                <span className="text-orange-600">
                  Resets in {getResetTimeMinutes()} minute{getResetTimeMinutes() !== 1 ? 's' : ''}
                </span>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Chat Interface */}
        <Card className="border-slate-200 shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-lg">
              <MessageSquare className="w-5 h-5 text-blue-500" />
              Sesi Obrolanmu
            </CardTitle>
            <Separator />
          </CardHeader>

          <CardContent className="p-0">
            {/* Messages Container with Fixed Height and Proper Overflow */}
            <div className="h-[500px] flex flex-col">
              <ScrollArea className="flex-1 px-6">
                <div className="space-y-4 py-4">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex gap-3 ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      {message.sender === 'bot' && (
                        <div className="flex-shrink-0 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                          <Bot className="w-4 h-4 text-white" />
                        </div>
                      )}

                      <div className={`max-w-xs lg:max-w-md xl:max-w-lg ${message.sender === 'user' ? 'order-1' : ''}`}>
                        <div
                          className={`rounded-lg px-4 py-3 break-words ${
                            message.sender === 'user'
                              ? 'bg-blue-500 text-white'
                              : 'bg-white border border-slate-200'
                          }`}
                        >
                          <div className={`prose prose-sm max-w-none ${message.sender === 'user' ? 'prose-invert text-white' : 'dark:prose-invert'}`}>
                            <ReactMarkdown
                              components={{
                                ...components,
                                // Ensure all text elements are white in user messages
                                p: ({node, ...props}) => (
                                  <p className={message.sender === 'user' ? 'text-white' : ''} {...props} />
                                )
                              }}
                              remarkPlugins={[remarkGfm]}
                              rehypePlugins={[rehypeHighlight]}
                            >
                              {message.text}
                            </ReactMarkdown>
                          </div>

                          <div className="flex justify-end mt-1">
                            <button
                              onClick={() => handleCopyToClipboard(message.text, message.id)}
                              className={`text-xs flex items-center gap-1 p-1 rounded transition-colors ${message.sender === 'user' ? 'text-white hover:text-white/90 hover:bg-white/10' : 'text-slate-500 hover:text-slate-700 hover:bg-slate-100'}` }
                              title="Copy to clipboard"
                            >
                              {copiedMessageId === message.id ? (
                                <>
                                  <Check className="w-3 h-3 text-green-500" />
                                  <span>Copied!</span>
                                </>
                              ) : (
                                <Copy className="w-3 h-3" />
                              )}
                            </button>
                          </div>

                          {message.sender === 'bot' && (
                            <div className="flex flex-col gap-2 mt-2 pt-2 border-t border-slate-100">
                              <div className="flex items-center gap-2">
                                <div className="flex items-center gap-1">
                                  {message.fromKnowledgeBase ? (
                                    <CheckCircle className="w-3 h-3 text-green-500" />
                                  ) : (
                                    <XCircle className="w-3 h-3 text-red-500" />
                                  )}
                                  <span className={`text-xs ${
                                    message.fromKnowledgeBase ? 'text-green-600' : 'text-red-600'
                                  }`}>
                                    {message.fromKnowledgeBase ? 'Knowledge Base' : 'No Source'}
                                  </span>
                                </div>
                              </div>

                              {/* Show source files with clickable URLs */}
                              {message.source && message.source.trim() && (
                                <div className="flex flex-wrap gap-1">
                                  {message.source.split(', ').map((sourceName, index) => {
                                    const sourceUrl = message.sourceUrls?.[sourceName];

                                    if (sourceUrl) {
                                      return (
                                        <Button
                                          key={index}
                                          variant="outline"
                                          size="sm"
                                          className="text-xs h-6 px-2 py-0 hover:bg-blue-50"
                                          onClick={() => handleSourceClick(sourceUrl)}
                                        >
                                          <ExternalLink className="w-3 h-3 mr-1" />
                                          {sourceName}
                                        </Button>
                                      );
                                    } else {
                                      return (
                                        <Badge key={index} variant="outline" className="text-xs">
                                          {sourceName}
                                        </Badge>
                                      );
                                    }
                                  })}
                                </div>
                              )}
                            </div>
                          )}
                        </div>

                        <p className="text-xs text-slate-500 mt-1 px-1">
                          {message.timestamp.toLocaleTimeString([], {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </p>
                      </div>

                      {message.sender === 'user' && (
                        <div className="flex-shrink-0 w-8 h-8 bg-slate-500 rounded-full flex items-center justify-center order-2">
                          <User className="w-4 h-4 text-white" />
                        </div>
                      )}
                    </div>
                  ))}

                  {isProcessing && (
                    <div className="flex gap-3 justify-start">
                      <div className="flex-shrink-0 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <Bot className="w-4 h-4 text-white" />
                      </div>
                      <div className="bg-white border border-slate-200 rounded-lg px-4 py-3">
                        <div className="flex items-center gap-2">
                          <div className="flex gap-1">
                            <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"></div>
                            <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                            <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                          </div>
                          <span className="text-sm text-slate-500">Searching knowledge base...</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                <div ref={messagesEndRef} />
              </ScrollArea>
            </div>

            {/* Input Area */}
            <div className="border-t border-slate-200 p-4">
              <div className="flex gap-2">
                <Input
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Tanya soal hal-hal yang aku tahu..."
                  disabled={isProcessing || getRemainingRequests() === 0}
                  className="flex-1"
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={!inputText.trim() || isProcessing || getRemainingRequests() === 0}
                  size="icon"
                >
                  <Send className="w-4 h-4" />
                </Button>
              </div>

              <div className="flex items-center justify-between mt-2 text-xs text-slate-500">
                <span>Tekan Enter untuk mengirim • Cek ulang informasi, AI bisa salah.</span>
                <span>Sisa {getRemainingRequests()} request</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
