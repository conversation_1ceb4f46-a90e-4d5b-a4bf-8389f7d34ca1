#!/usr/bin/env node

const http = require('http');

const BASE_URL = 'http://localhost:3000';

function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL);
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const parsed = JSON.parse(body);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testRateLimitFix() {
  console.log('🧪 Testing Rate Limit Status Fix');
  console.log('=================================\n');

  try {
    // Test 1: Check initial rate limit status multiple times
    console.log('1️⃣ Testing rate limit status endpoint (should NOT consume tokens)');
    
    const initialCheck = await makeRequest('/api/rate-limit-status');
    console.log('📊 Initial status:', JSON.stringify(initialCheck.data, null, 2));
    const initialRemaining = initialCheck.data.remaining;

    // Check status multiple times - this should NOT decrease the counter
    console.log('\n🔄 Checking status 5 times in a row...');
    for (let i = 1; i <= 5; i++) {
      const check = await makeRequest('/api/rate-limit-status');
      console.log(`   Check ${i}: remaining = ${check.data.remaining}`);
      
      if (check.data.remaining !== initialRemaining) {
        console.log('❌ FAILED: Rate limit status check consumed a token!');
        return;
      }
    }
    console.log('✅ SUCCESS: Rate limit status checks did not consume tokens\n');

    // Test 2: Make an actual chat request (should consume a token)
    console.log('2️⃣ Making actual chat request (should consume 1 token)');
    const chatResponse = await makeRequest('/api/chat-public', 'POST', { message: 'test' });
    console.log('💬 Chat response status:', chatResponse.status);

    // Check status after chat request
    const afterChatCheck = await makeRequest('/api/rate-limit-status');
    console.log('📊 After chat request:', JSON.stringify(afterChatCheck.data, null, 2));
    
    const expectedRemaining = initialRemaining - 1;
    if (afterChatCheck.data.remaining === expectedRemaining) {
      console.log('✅ SUCCESS: Chat request consumed exactly 1 token\n');
    } else {
      console.log(`❌ FAILED: Expected ${expectedRemaining} remaining, got ${afterChatCheck.data.remaining}\n`);
    }

    // Test 3: Check status again after chat (should not consume more tokens)
    console.log('3️⃣ Checking status again after chat (should NOT consume more tokens)');
    const finalCheck = await makeRequest('/api/rate-limit-status');
    console.log('📊 Final status:', JSON.stringify(finalCheck.data, null, 2));
    
    if (finalCheck.data.remaining === expectedRemaining) {
      console.log('✅ SUCCESS: Status check after chat did not consume additional tokens');
    } else {
      console.log('❌ FAILED: Status check after chat consumed additional tokens');
    }

    console.log('\n🎉 Rate limit fix test completed!');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

testRateLimitFix();
