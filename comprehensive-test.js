const http = require('http');

function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          resolve({ status: res.statusCode, data: JSON.parse(body) });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function test() {
  console.log('🧪 Comprehensive Rate Limit Fix Test');
  console.log('====================================\n');
  
  try {
    // Test 1: Check that status endpoint doesn't consume tokens
    console.log('1️⃣ Testing rate limit status endpoint (should NOT consume tokens)');
    
    const initial = await makeRequest('/api/rate-limit-status');
    console.log(`Initial: remaining = ${initial.data.remaining}`);
    
    // Check status multiple times
    for (let i = 1; i <= 3; i++) {
      const check = await makeRequest('/api/rate-limit-status');
      console.log(`Status check ${i}: remaining = ${check.data.remaining}`);
      
      if (check.data.remaining !== initial.data.remaining) {
        console.log('❌ FAILED: Status check consumed a token!');
        return;
      }
    }
    console.log('✅ SUCCESS: Status checks did not consume tokens\n');
    
    // Test 2: Check that chat requests DO consume tokens
    console.log('2️⃣ Testing chat endpoint (should consume tokens)');
    
    const beforeChat = await makeRequest('/api/rate-limit-status');
    console.log(`Before chat: remaining = ${beforeChat.data.remaining}`);
    
    const chatResponse = await makeRequest('/api/chat-public', 'POST', { message: 'test' });
    console.log(`Chat response status: ${chatResponse.status}`);
    
    const afterChat = await makeRequest('/api/rate-limit-status');
    console.log(`After chat: remaining = ${afterChat.data.remaining}`);
    
    if (afterChat.data.remaining === beforeChat.data.remaining - 1) {
      console.log('✅ SUCCESS: Chat request consumed exactly 1 token\n');
    } else {
      console.log(`❌ FAILED: Expected ${beforeChat.data.remaining - 1}, got ${afterChat.data.remaining}\n`);
    }
    
    // Test 3: Verify status check after chat doesn't consume more
    console.log('3️⃣ Testing status check after chat (should NOT consume more tokens)');
    
    const finalCheck = await makeRequest('/api/rate-limit-status');
    console.log(`Final check: remaining = ${finalCheck.data.remaining}`);
    
    if (finalCheck.data.remaining === afterChat.data.remaining) {
      console.log('✅ SUCCESS: Status check after chat did not consume additional tokens');
    } else {
      console.log('❌ FAILED: Status check consumed additional tokens');
    }
    
    console.log('\n🎉 Rate limit fix test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

test();
