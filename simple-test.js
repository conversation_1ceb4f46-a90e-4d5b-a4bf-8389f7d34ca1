const http = require('http');

function makeRequest(path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(body));
        } catch (e) {
          resolve(body);
        }
      });
    });

    req.on('error', reject);
    req.end();
  });
}

async function test() {
  console.log('Testing rate limit status endpoint...\n');
  
  try {
    // Check status 3 times
    for (let i = 1; i <= 3; i++) {
      const result = await makeRequest('/api/rate-limit-status');
      console.log(`Check ${i}: remaining = ${result.remaining}, success = ${result.success}`);
      
      // Wait a bit between requests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log('\n✅ If the remaining count stayed the same, the fix is working!');
  } catch (error) {
    console.error('Error:', error.message);
  }
}

test();
